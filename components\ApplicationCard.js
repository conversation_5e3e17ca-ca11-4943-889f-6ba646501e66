import { useState } from "react";
import { useRouter } from "next/router";
import { formatDate } from "../lib/utils";
import {
  FileText,
  Check,
  X,
  Clock,
  User,
  Building,
  Calendar,
  DollarSign,
  MessageSquare,
  ExternalLink,
  AlertCircle
} from "lucide-react";

export default function ApplicationCard({ application, user, onUpdate, className = "" }) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleAction = async (action) => {
    setLoading(true);
    try {
      const res = await fetch(`/api/applications/${application.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      });

      if (res.ok) {
        onUpdate?.();
      } else {
        const error = await res.json();
        console.error("Failed to update application:", error);
      }
    } catch (error) {
      console.error("Failed to update application:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = () => {
    const statusConfig = {
      pending: { className: "status-pending", icon: Clock, label: "Pending" },
      accepted: { className: "status-accepted", icon: Check, label: "Accepted" },
      rejected: { className: "status-rejected", icon: X, label: "Rejected" },
      in_progress: { className: "status-in-progress", icon: FileText, label: "In Progress" },
      completed: { className: "status-completed", icon: Check, label: "Completed" },
    };

    const config = statusConfig[application.status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span className={`${config.className} flex items-center gap-1`}>
        <Icon size={12} />
        {config.label}
      </span>
    );
  };

  const getPaymentDisplay = () => {
    if (application.payment_type === "percentage") {
      return `${application.payment_percentage}% recurring`;
    }
    return `$${application.payment_fixed} fixed`;
  };

  const getDaysAgo = () => {
    const days = Math.floor((new Date() - new Date(application.created_at)) / (1000 * 60 * 60 * 24));
    if (days === 0) return "Today";
    if (days === 1) return "Yesterday";
    return `${days} days ago`;
  };

  return (
    <div className={`card card-hover group ${className}`}>
      <div className="card-content">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-semibold mb-1 truncate group-hover:text-primary transition-colors">
              {user.user_type === "seeker"
                ? application.referrer_name || "Unknown Referrer"
                : application.job_title || "Unknown Job"}
            </h4>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar size={14} />
              <span>{getDaysAgo()}</span>
              <span>•</span>
              <span>{formatDate(application.created_at)}</span>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {getStatusBadge()}
          </div>
        </div>

        {/* Application Details */}
        <div className="space-y-3 mb-4">
          {user.user_type === "referrer" && application.seeker_name && (
            <div className="flex items-center gap-2 text-sm">
              <User size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Job Seeker:</span>
              <span className="font-medium">{application.seeker_name}</span>
            </div>
          )}

          {application.company_name && (
            <div className="flex items-center gap-2 text-sm">
              <Building size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Company:</span>
              <span className="font-medium">{application.company_name}</span>
            </div>
          )}

          {(application.payment_type || application.payment_percentage || application.payment_fixed) && (
            <div className="flex items-center gap-2 text-sm">
              <DollarSign size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Payment:</span>
              <span className="font-medium">{getPaymentDisplay()}</span>
            </div>
          )}

          {application.job_role && (
            <div className="flex items-center gap-2">
              <span className="badge-secondary text-xs">{application.job_role}</span>
            </div>
          )}
        </div>

        {/* Message */}
        {application.message && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <MessageSquare size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Message:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg border-l-2 border-border">
              {application.message}
            </p>
          </div>
        )}

        {/* Position Details */}
        {application.position_details && (
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText size={14} className="text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Position Details:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg">
              {application.position_details}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex flex-col sm:flex-row gap-2 justify-between items-start">
          <div className="flex flex-wrap gap-2">
            {application.status === "pending" && user.user_type === "seeker" && (
              <>
                <button
                  onClick={() => handleAction("accept")}
                  disabled={loading}
                  className="btn-primary btn-sm flex items-center gap-2"
                >
                  {loading ? (
                    <div className="w-3 h-3 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Check size={14} />
                  )}
                  Accept
                </button>
                <button
                  onClick={() => handleAction("reject")}
                  disabled={loading}
                  className="btn-outline btn-sm flex items-center gap-2 text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground"
                >
                  {loading ? (
                    <div className="w-3 h-3 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <X size={14} />
                  )}
                  Reject
                </button>
              </>
            )}

            {application.status === "accepted" && application.contract_id && (
              <button
                onClick={() => router.push(`/contract/${application.contract_id}`)}
                className="btn-primary btn-sm flex items-center gap-2"
              >
                <ExternalLink size={14} />
                View Contract
              </button>
            )}

            {application.status === "accepted" && !application.contract_id && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircle size={14} />
                <span>Contract pending</span>
              </div>
            )}
          </div>

          {/* Additional Info */}
          <div className="text-xs text-muted-foreground">
            ID: {application.id}
          </div>
        </div>
      </div>
    </div>
  );
}
