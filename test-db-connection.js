const mysql = require("mysql2/promise");
const fs = require('fs');

// Load environment variables manually
const envContent = fs.readFileSync('.env.local', 'utf8');
const envLines = envContent.split('\n');
envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    let value = valueParts.join('=');
    // Remove quotes if present
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    process.env[key] = value;
  }
});

async function testConnection() {
  console.log('Testing database connection...');
  console.log('Host:', process.env.DB_HOST);
  console.log('User:', process.env.DB_USER);
  console.log('Database:', process.env.DB_NAME);
  console.log('Password length:', process.env.DB_PASSWORD?.length);

  const configs = [
    // Config 1: Basic connection
    {
      name: 'Basic Connection',
      config: {
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
      }
    },
    // Config 2: With SSL disabled
    {
      name: 'SSL Disabled',
      config: {
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        ssl: false
      }
    },
    // Config 3: With SSL but reject unauthorized false
    {
      name: 'SSL Reject Unauthorized False',
      config: {
        host: process.env.DB_HOST,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        ssl: {
          rejectUnauthorized: false
        }
      }
    },
    // Config 4: With port specified
    {
      name: 'With Port 3306',
      config: {
        host: process.env.DB_HOST,
        port: 3306,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        ssl: {
          rejectUnauthorized: false
        }
      }
    }
  ];

  for (const { name, config } of configs) {
    try {
      console.log(`\n--- Testing ${name} ---`);
      const connection = await mysql.createConnection(config);
      console.log(`✅ ${name}: Connection successful`);
      
      // Test a simple query
      const [rows] = await connection.execute('SELECT 1 as test');
      console.log(`✅ ${name}: Query successful`, rows);
      
      await connection.end();
    } catch (error) {
      console.log(`❌ ${name}: Connection failed`);
      console.log('Error code:', error.code);
      console.log('Error message:', error.message);
      if (error.sqlMessage) {
        console.log('SQL Message:', error.sqlMessage);
      }
    }
  }
}

testConnection().catch(console.error);
