import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  const { id } = req.query;

  if (req.method === "GET") {
    try {
      const applications = await query(
        `
        SELECT 
          a.*,
          jp.title as job_title,
          jp.user_id as seeker_id,
          c.id as contract_id
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        LEFT JOIN contracts c ON c.application_id = a.id
        WHERE a.id = ?
      `,
        [id]
      );

      if (applications.length === 0) {
        return res.status(404).json({ error: "Application not found" });
      }

      const application = applications[0];

      // Verify user has access
      if (
        req.user.id !== application.seeker_id &&
        req.user.id !== application.referrer_id
      ) {
        return res.status(403).json({ error: "Access denied" });
      }

      res.status(200).json({ application });
    } catch (error) {
      console.error("Failed to fetch application:", error);
      res.status(500).json({ error: "Failed to fetch application" });
    }
  } else if (req.method === "PATCH") {
    // Previous PATCH logic here...
  } else {
    res.status(405).json({ error: "Method not allowed" });
  }
});
