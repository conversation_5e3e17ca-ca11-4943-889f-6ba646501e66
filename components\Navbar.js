import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { Menu, X, User, LogOut, ChevronDown, Briefcase, Settings, Bell } from "lucide-react";

export default function Navbar({ user }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const router = useRouter();

  const handleLogout = async () => {
    await fetch("/api/auth/logout", { method: "POST" });
    router.push("/");
    window.location.reload();
  };

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [router.asPath]);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuOpen && !event.target.closest('.user-menu')) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen]);

  const isActive = (path) => router.pathname === path;

  return (
    <nav className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b border-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Briefcase size={20} className="text-primary-foreground" />
            </div>
            <span className="text-xl font-bold tracking-tight">Split Job</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/explore"
              className={`nav-link ${isActive('/explore') ? 'nav-link-active' : ''}`}
            >
              Explore Jobs
            </Link>

            {user && (
              <Link
                href="/dashboard"
                className={`nav-link ${isActive('/dashboard') ? 'nav-link-active' : ''}`}
              >
                Dashboard
              </Link>
            )}

            {user ? (
              <div className="flex items-center space-x-4">
                {/* Notifications */}
                <button className="relative p-2 text-muted-foreground hover:text-foreground transition-colors">
                  <Bell size={20} />
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs"></span>
                </button>

                {/* User Menu */}
                <div className="relative user-menu">
                  <button
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-accent transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <User size={16} className="text-primary-foreground" />
                    </div>
                    <span className="text-sm font-medium hidden lg:block">{user.full_name}</span>
                    <ChevronDown size={16} className={`transition-transform ${userMenuOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* User Dropdown */}
                  {userMenuOpen && (
                    <div className="absolute right-0 mt-2 w-56 bg-popover border border-border rounded-lg shadow-lg animate-in slide-down">
                      <div className="p-3 border-b border-border">
                        <p className="text-sm font-medium">{user.full_name}</p>
                        <p className="text-xs text-muted-foreground">{user.email}</p>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary text-secondary-foreground mt-1">
                          {user.user_type === 'seeker' ? 'Job Seeker' : 'Referrer'}
                        </span>
                      </div>

                      <div className="py-1">
                        <Link
                          href="/profile"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <User size={16} className="mr-2" />
                          Profile
                        </Link>
                        <Link
                          href="/applications"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <Briefcase size={16} className="mr-2" />
                          Applications
                        </Link>
                        <Link
                          href="/settings"
                          className="flex items-center px-3 py-2 text-sm hover:bg-accent transition-colors"
                        >
                          <Settings size={16} className="mr-2" />
                          Settings
                        </Link>
                      </div>

                      <div className="border-t border-border py-1">
                        <button
                          onClick={handleLogout}
                          className="flex items-center w-full px-3 py-2 text-sm text-destructive hover:bg-accent transition-colors"
                        >
                          <LogOut size={16} className="mr-2" />
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  href="/login"
                  className="btn-ghost"
                >
                  Login
                </Link>
                <Link
                  href="/signup"
                  className="btn-primary"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-accent transition-colors"
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden border-t border-border animate-in slide-down">
            <div className="py-4 space-y-2">
              <Link
                href="/explore"
                className={`block px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isActive('/explore') ? 'bg-accent text-accent-foreground' : 'hover:bg-accent'
                }`}
              >
                Explore Jobs
              </Link>

              {user && (
                <Link
                  href="/dashboard"
                  className={`block px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                    isActive('/dashboard') ? 'bg-accent text-accent-foreground' : 'hover:bg-accent'
                  }`}
                >
                  Dashboard
                </Link>
              )}

              {user ? (
                <>
                  <div className="border-t border-border pt-4 mt-4">
                    <div className="px-3 py-2">
                      <p className="text-sm font-medium">{user.full_name}</p>
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </div>
                  </div>

                  <Link
                    href="/profile"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Profile
                  </Link>
                  <Link
                    href="/applications"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Applications
                  </Link>
                  <Link
                    href="/settings"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Settings
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-3 py-2 rounded-lg text-sm font-medium text-destructive hover:bg-accent transition-colors"
                  >
                    Logout
                  </button>
                </>
              ) : (
                <div className="border-t border-border pt-4 mt-4 space-y-2">
                  <Link
                    href="/login"
                    className="block px-3 py-2 rounded-lg text-sm font-medium hover:bg-accent transition-colors"
                  >
                    Login
                  </Link>
                  <Link
                    href="/signup"
                    className="block px-3 py-2 rounded-lg text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
