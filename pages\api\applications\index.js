import { withAuth } from "../../../lib/middleware";
import { query } from "../../../lib/db";

export default withAuth(async (req, res) => {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const {
      status = "all",
      page = 1,
      limit = 10,
      sortBy = "created_at",
      sortOrder = "DESC",
      search = "",
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validStatuses = ["pending", "accepted", "rejected", "in_progress", "completed"];
    const validSortFields = ["created_at", "status", "job_title"];

    // Build WHERE clause for status filter
    let statusFilter = "";
    if (status !== "all" && validStatuses.includes(status)) {
      statusFilter = `AND a.status = '${status}'`;
    }

    // Build search filter
    let searchFilter = "";
    if (search.trim()) {
      searchFilter = `AND (jp.title LIKE '%${search}%' OR a.company_name LIKE '%${search}%' OR a.message LIKE '%${search}%')`;
    }

    // Validate sort parameters
    const sortField = validSortFields.includes(sortBy) ? sortBy : "created_at";
    const sortDirection = sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC";

    let applications;
    let countQuery;

    if (req.user.user_type === "seeker") {
      // Get applications received for seeker's job posts
      applications = await query(
        `
        SELECT
          a.*,
          jp.title as job_title,
          jp.job_role,
          jp.payment_type,
          jp.payment_percentage,
          jp.payment_fixed,
          u.full_name as referrer_name,
          u.email as referrer_email,
          u.linkedin_url as referrer_linkedin,
          u.github_url as referrer_github,
          u.portfolio_url as referrer_portfolio,
          c.id as contract_id,
          c.status as contract_status
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users u ON a.referrer_id = u.id
        LEFT JOIN contracts c ON c.application_id = a.id
        WHERE jp.user_id = ? ${statusFilter} ${searchFilter}
        ORDER BY ${sortField === "job_title" ? "jp.title" : "a." + sortField} ${sortDirection}
        LIMIT ? OFFSET ?
      `,
        [req.user.id, parseInt(limit), offset]
      );

      // Get total count for pagination
      countQuery = await query(
        `
        SELECT COUNT(*) as total
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        WHERE jp.user_id = ? ${statusFilter} ${searchFilter}
      `,
        [req.user.id]
      );
    } else {
      // Get applications made by referrer
      applications = await query(
        `
        SELECT
          a.*,
          jp.title as job_title,
          jp.job_role,
          jp.payment_type,
          jp.payment_percentage,
          jp.payment_fixed,
          u.full_name as seeker_name,
          u.email as seeker_email,
          u.linkedin_url as seeker_linkedin,
          u.github_url as seeker_github,
          u.portfolio_url as seeker_portfolio,
          c.id as contract_id,
          c.status as contract_status
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        JOIN users u ON jp.user_id = u.id
        LEFT JOIN contracts c ON c.application_id = a.id
        WHERE a.referrer_id = ? ${statusFilter} ${searchFilter}
        ORDER BY ${sortField === "job_title" ? "jp.title" : "a." + sortField} ${sortDirection}
        LIMIT ? OFFSET ?
      `,
        [req.user.id, parseInt(limit), offset]
      );

      // Get total count for pagination
      countQuery = await query(
        `
        SELECT COUNT(*) as total
        FROM applications a
        JOIN job_posts jp ON a.job_post_id = jp.id
        WHERE a.referrer_id = ? ${statusFilter} ${searchFilter}
      `,
        [req.user.id]
      );
    }

    const totalCount = countQuery[0].total;
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    // Add computed fields
    const enrichedApplications = applications.map((app) => ({
      ...app,
      payment_display:
        app.payment_type === "percentage"
          ? `${app.payment_percentage}% recurring`
          : `$${app.payment_fixed} fixed`,
      days_since_applied: Math.floor(
        (new Date() - new Date(app.created_at)) / (1000 * 60 * 60 * 24)
      ),
    }));

    res.status(200).json({
      applications: enrichedApplications,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1,
      },
      filters: {
        status,
        search,
        sortBy: sortField,
        sortOrder: sortDirection,
      },
    });
  } catch (error) {
    console.error("Failed to fetch applications:", error);
    res.status(500).json({ error: "Failed to fetch applications" });
  }
});