# 🔧 Database Setup Guide - <PERSON><PERSON><PERSON>

## Current Issue
Your remote MySQL database on <PERSON><PERSON> is denying access from your IP address `*************`. This is a common hosting provider security feature.

## 🚨 IMMEDIATE SOLUTION - Fix Hostinger Permissions

### Option 1: Hostinger Control Panel
1. Log into your **Hostinger Control Panel**
2. Go to **MySQL Databases** section
3. Find database: `u618120801_SplitJob_33`
4. Click **"Remote MySQL"** or **"Remote Access"**
5. Add your IP: `*************`
6. Or add `%` for any IP (less secure but more flexible)

### Option 2: Contact Hostinger Support
Ask them to:
- Grant remote access for user `u618120801_SplitJobUser3` from IP `*************`
- Or grant access from `%` (any IP)
- Verify user has correct privileges on database `u618120801_SplitJob_33`

## 🛠️ TEMPORARY SOLUTION - Local Development Database

While you fix the Hostinger issue, use a local MySQL database:

### Step 1: Install MySQL
- **Windows**: Download from https://dev.mysql.com/downloads/mysql/
- **macOS**: `brew install mysql`
- **Linux**: `sudo apt-get install mysql-server`

### Step 2: Start MySQL Service
- **Windows**: Start MySQL service from Services panel
- **macOS/Linux**: `sudo systemctl start mysql` or `brew services start mysql`

### Step 3: Create Local Database
```bash
# Connect to MySQL
mysql -u root -p

# Run the setup script
source setup-local-db.sql
```

### Step 4: Update Environment
Your `.env.local` is already configured for local development:
```
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=splitjob_local
```

## 🔄 Switch Back to Remote Database

Once Hostinger permissions are fixed, restore from backup:
```bash
cp .env.local.backup .env.local
```

## 🧪 Test the Connection

After setting up local database:
```bash
npm run dev
```

Then test signup at: http://localhost:3000/signup

## 📞 Need Help?

If you continue having issues:
1. Check MySQL is running: `mysql -u root -p`
2. Verify database exists: `SHOW DATABASES;`
3. Check user permissions: `SHOW GRANTS FOR 'root'@'localhost';`

## 🔐 Security Notes

- Local database has no password for development
- Remote database should use strong passwords
- Never commit real passwords to version control
- Use environment variables for all credentials
